<template>
  <div>
    <!-- Task List Header -->
    <div class="flex items-center justify-between">
      <label class="font-semibold text-sm">Danh sách công việc</label>
      <div
        @click="handleCreateWorkEffort"
        class="bg-primary text-white px-2 py-1 rounded flex items-center cursor-pointer"
      >
        {{
          dataWorkEffort[0]?.workflowId === "CHECK_IN"
            ? "Kết thúc ca"
            : "Bắt đầu ca"
        }}
      </div>
    </div>

    <!-- Task List -->
    <div class="space-y-3 mt-3 md:h-[45vh] h-[65vh] overflow-y-auto">
      <div
        v-for="task in dataWorkEffort"
        :key="task.id"
        @click="handleSelectWorkEffort(task)"
        class="rounded-lg p-3 transition-all duration-200 cursor-pointer bg-white border border-gray-200 hover:shadow-sm hover:border-gray-300"
      >
        <!-- Task Header -->
        <div class="flex items-start justify-between mb-2">
          <div class="flex-1 min-w-0">
            <h4 class="text-sm font-medium truncate text-gray-900">
              {{ task?.name }}
            </h4>
            <p class="text-xs mt-1 text-gray-500"></p>
          </div>
          <div v-if="!isMobile">
            <!-- Nút chụp ảnh nếu chưa có ảnh -->
            <div
              v-if="!hasImages(task.id)"
              class="bg-primary text-white px-2 py-1 rounded cursor-pointer hover:bg-primary-dark transition-colors"
              @click.stop="handleOpenCamera(task)"
            >
              <svg
                class="w-3 h-3 inline mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                />
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
              Chụp ảnh
            </div>

            <!-- Nút xem ảnh nếu đã có ảnh -->
            <div
              v-else
              class="bg-green-500 text-white px-2 py-1 rounded cursor-pointer hover:bg-green-600 transition-colors"
              @click.stop="handleViewImages(task)"
            >
              <svg
                class="w-3 h-3 inline mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              Xem ảnh ({{ getImageCount(task.id) }})
            </div>
          </div>
          <!-- Selected indicator -->
          <div
            v-if="selectedWorkEffortId?.id === task.id && isMobile"
            class="flex items-center justify-center w-6 h-6 bg-primary rounded-full"
          >
            <svg
              class="w-3 h-3 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
        </div>

        <!-- Task Details -->
        <div class="space-y-2">
          <!-- Assignee -->
          <div class="flex items-center gap-2">
            <svg
              class="w-3 h-3 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
            <span class="text-xs cursor-help text-gray-600">
              {{ getEmployeeName(task?.createdBy) }}
            </span>
          </div>
          <!-- Time -->
          <div class="flex items-center justify-between md:pb-0 pb-2">
            <div class="flex items-center gap-2">
              <svg
                class="w-3 h-3 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span class="text-xs text-gray-600">
                {{ formatTimestampV3(task?.createdStamp) }}
              </span>
            </div>
            <button
              v-if="task?.status !== 4"
              @click.stop="handleCompleteWorkEffort(task)"
              class="px-3 py-1 text-xs bg-green-500 hover:bg-green-600 text-white rounded-md transition-colors duration-200"
            >
              Hoàn thành
            </button>
            <span
              v-else
              class="px-3 py-1 text-xs bg-gray-100 text-gray-500 rounded-md"
            >
              Đã hoàn thành
            </span>
          </div>
        </div>
        <!-- task editor -->
        <Editor
          v-if="!isMobile"
          @autosave="(data:any) => handleUpdateContent(data, task?.id)"
          :data="getTaskContent(task?.id)"
          :placeholder="'Nhập nội dung công việc...'"
          :showCharacterCount="true"
          :editable="!isLoading"
          :minHeight="'250px'"
          :autosave="true"
          class="transition-opacity duration-200"
          :class="{ 'opacity-50 pointer-events-none': isLoading }"
        ></Editor>
      </div>
    </div>
    <ModalTimeKeeping
      v-if="isOpenCamera"
      @isClose="isOpenCamera = false"
      @uploadImage="handleUploadImage"
    />

    <!-- Modal xem ảnh -->
    <div
      v-if="showImageModal"
      class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
      @click="closeImageModal"
    >
      <div class="relative max-w-4xl max-h-[90vh] w-full mx-4">
        <!-- Header -->
        <div class="bg-white p-4 rounded-t-lg">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">
              Ảnh chấm công - {{ selectedTaskForImages?.name }}
            </h3>
            <button
              @click="closeImageModal"
              class="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg
                class="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        <!-- Image Grid -->
        <div class="bg-white p-4 max-h-[70vh] overflow-y-auto">
          <div
            v-if="currentTaskImages.length > 0"
            class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"
          >
            <div
              v-for="(image, index) in currentTaskImages"
              :key="image.id || index"
              class="relative group bg-gray-100 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200"
            >
              <img
                :src="getImageUrl(image)"
                :alt="`Ảnh chấm công ${index + 1}`"
                class="w-full h-48 object-cover cursor-pointer transition-transform duration-200 group-hover:scale-105"
                @click="openFullImage(getImageUrl(image))"
                loading="lazy"
              />
              <div
                class="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded"
              >
                {{ index + 1 }}/{{ currentTaskImages.length }}
              </div>
            </div>
          </div>
          <div v-else class="text-center py-8 text-gray-500">
            Không có ảnh chấm công nào
          </div>
        </div>

        <!-- Footer -->
        <div
          class="bg-gray-50 p-4 rounded-b-lg flex justify-between items-center"
        >
          <span class="text-sm text-gray-600">
            Tổng cộng: {{ currentTaskImages.length }} ảnh
          </span>
          <button
            @click="closeImageModal"
            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Đóng
          </button>
        </div>
      </div>
    </div>

    <!-- Modal xem ảnh full size -->
    <div
      v-if="fullImageUrl"
      class="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-60"
      @click="closeFullImage"
    >
      <div class="relative max-w-[95vw] max-h-[95vh]">
        <img
          :src="fullImageUrl"
          alt="Ảnh full size"
          class="max-w-full max-h-full object-contain"
          @click.stop
        />
        <button
          @click="closeFullImage"
          class="absolute top-4 right-4 text-white hover:text-gray-300 transition-colors"
        >
          <svg
            class="w-8 h-8"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";

const emit = defineEmits([
  "createWorkEffort",
  "selectWorkEffort",
  "dataEmployee",
  "completeWorkEffort",
]);

const props = defineProps<{
  dataWorkEffort: any[];
  selectedWorkEffortId: any | null;
  isMobile: Boolean;
  dataEmployee?: any[];
}>();

// Computed properties
const getEmployeeName = (createdBy: string) => {
  if (!createdBy) return "Không xác định";

  if (!props.dataEmployee || !Array.isArray(props.dataEmployee)) {
    return createdBy; // Trả về createdBy gốc nếu không có dataEmployee
  }
  const employee = props.dataEmployee.find((emp: any) => {
    return emp.id === createdBy;
  });
  if (employee) {
    const employeeName = employee.name;
    if (employeeName !== createdBy && employeeName !== employee.id) {
      return employeeName;
    }
  }
  return createdBy;
};

// Functions
const handleCreateWorkEffort = () => {
  emit("createWorkEffort");
};

const handleSelectWorkEffort = (workEffort: any) => {
  if (!props.isMobile) return;
  emit("selectWorkEffort", workEffort);
};
// Methods
const { getContent, handleSaveTextEditor } = usePortal();
const isLoading = ref(false);
const taskContents = ref<Record<string, string>>({});
const auth = useCookie("auth") as any;
// Hàm cập nhật content cho task cụ thể
const handleUpdateContent = async (data: any, taskId: string) => {
  if (!taskId) {
    console.error("TaskId is required");
    return;
  }

  try {
    // Cập nhật cache local
    taskContents.value[taskId] = data;

    // Tạo payload để gửi lên server
    const payload = {
      type: "DESCRIPTION_WORKEFFORT",
      content: data,
      relativeId: taskId,
      version: new Date(),
      createdBy: auth.value?.user?.id, // Có thể thay bằng user hiện tại
    };

    // Gọi API để lưu
    await handleSaveTextEditor(payload);
  } catch (error) {
    console.error("Error saving content for task:", taskId, error);
  }
};

// Hàm lấy content cho task cụ thể
const getTaskContent = (taskId: string) => {
  if (!taskId) return "";

  // Nếu đã có trong cache, trả về ngay
  if (taskContents.value[taskId] !== undefined) {
    return taskContents.value[taskId];
  }

  // Nếu chưa có, load từ server
  loadTaskContent(taskId);
  return "";
};

// Hàm load content từ server
const loadTaskContent = async (taskId: string) => {
  if (!taskId || isLoading.value || !props.isMobile) return;

  try {
    isLoading.value = true;
    const response = await getContent("DESCRIPTION_WORKEFFORT", taskId);
    taskContents.value[taskId] = response?.content || "";
  } catch (error) {
    console.error("Error loading task content:", error);
    taskContents.value[taskId] = "";
  } finally {
    isLoading.value = false;
  }
};
const handleCompleteWorkEffort = async (task: any) => {
  emit("completeWorkEffort", task);
};
const { uploadImage, getImage } = useFileService();
const taskImages = ref<Record<string, any[]>>({});

// Lấy ảnh cho task cụ thể
const handleGetImage = async (taskId: string) => {
  try {
    const response = await getImage(taskId, "WORK_EFFORT_TIMEKEEPING");
    taskImages.value[taskId] = response || [];
    return response;
  } catch (error) {
    console.error("Error getting images for task:", taskId, error);
    taskImages.value[taskId] = [];
    return [];
  }
};

// Kiểm tra xem task có ảnh không
const hasImages = (taskId: string) => {
  return taskImages.value[taskId] && taskImages.value[taskId].length > 0;
};

// Lấy số lượng ảnh của task
const getImageCount = (taskId: string) => {
  return taskImages.value[taskId]?.length || 0;
};

// Computed property cho ảnh của task hiện tại
const currentTaskImages = computed(() => {
  if (!selectedTaskForImages.value) return [];
  return taskImages.value[selectedTaskForImages.value.id] || [];
});

// Lấy URL ảnh
const { getUrlImageTimeKeeping } = useTimeKeeping();
const getImageUrl = (image: any) => {
  if (!image) return "";

  // Sử dụng getUrlImageTimeKeeping() để lấy base URL và ghép với fileName
  if (image.fileName) {
    const baseUrl = getUrlImageTimeKeeping();
    return `${baseUrl}/${image.filePath}`;
  }

  // Fallback: nếu image có srcPath
  if (image.srcPath) {
    const baseUrl = getUrlImageTimeKeeping();
    return `${baseUrl}/${image.srcPath}`;
  }

  // Fallback: nếu image có path
  if (image.path) {
    const baseUrl = getUrlImageTimeKeeping();
    return `${baseUrl}/${image.path}`;
  }

  // Fallback: nếu image có url trực tiếp
  if (image.url) {
    return image.url;
  }

  // Fallback: return image object as string nếu nó là URL
  if (typeof image === "string") {
    return image;
  }

  return "";
};
const handleUploadImage = async (Image: any) => {
  isLoading.value = true;
  try {
    await uploadImage(
      Image,
      "public",
      workEffortId.value,
      "WORK_EFFORT_TIMEKEEPING"
    );
    await handleGetImage(workEffortId.value);
    useNuxtApp().$toast.success("Chụp ảnh thành công");
  } catch (error) {
    useNuxtApp().$toast.error("Chụp ảnh thất bại");
    throw error;
  } finally {
    isOpenCamera.value = false;
    isLoading.value = false;
  }
};

// Xử lý xem ảnh
const handleViewImages = async (task: any) => {
  selectedTaskForImages.value = task;

  // Load ảnh nếu chưa có
  if (!taskImages.value[task.id] || taskImages.value[task.id].length === 0) {
    await handleGetImage(task.id);
  }

  showImageModal.value = true;
};

// Đóng modal xem ảnh
const closeImageModal = () => {
  showImageModal.value = false;
  selectedTaskForImages.value = null;
};

// Mở ảnh full size
const openFullImage = (imageUrl: string) => {
  fullImageUrl.value = imageUrl;
};

// Đóng ảnh full size
const closeFullImage = () => {
  fullImageUrl.value = null;
};
const isOpenCamera = ref(false);
const workEffortId = ref("");

// Image modal state
const showImageModal = ref(false);
const selectedTaskForImages = ref<any>(null);
const fullImageUrl = ref<string | null>(null);
const handleOpenCamera = (task: any) => {
  isOpenCamera.value = true;
  workEffortId.value = task?.id;
};

// Load ảnh cho tất cả tasks khi component mount hoặc khi dataWorkEffort thay đổi
const loadAllTaskImages = async () => {
  if (!props.dataWorkEffort || props.dataWorkEffort.length === 0) return;

  for (const task of props.dataWorkEffort) {
    if (task.id && !taskImages.value[task.id]) {
      await handleGetImage(task.id);
    }
  }
};

// Watch dataWorkEffort để load ảnh khi có task mới
watch(
  () => props.dataWorkEffort,
  () => {
    loadAllTaskImages();
  },
  { immediate: true, deep: true }
);
</script>

<style scoped>
/* Custom scrollbar */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}
</style>
