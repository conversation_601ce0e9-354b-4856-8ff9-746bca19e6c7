<template>
  <div class="p-2 h-full flex flex-col overflow-hidden">
    <div class="grid grid-cols-1 md:grid-cols-12 gap-2 flex-1 min-h-0">
      <div class="col-span-12 md:col-span-4 flex flex-col min-h-0">
        <NavigationTimeKeeping
          @createWorkEffort="handleCreateWorkEffort"
          @selectWorkEffort="handleSelectWorkEffort"
          @employeeSelected="handleSelectedEmployee"
          @dateRangeChanged="handleChangeDate"
          @completeWorkEffort="completeWorkEffort"
          :dataWorkEffort="dataWorkEffort"
          :selectedWorkEffortId="selectedWorkEffortId"
          :dataEmployee="dataEmployee"
        ></NavigationTimeKeeping>
      </div>

      <div class="col-span-12 md:col-span-8 hidden md:flex flex-col min-h-0">
        <TabTimeKeeping
          :selectedWorkEffortId="selectedWorkEffortId"
        ></TabTimeKeeping>
      </div>
    </div>
    <LoadingSpinner v-if="isLoading"></LoadingSpinner>
  </div>
</template>

<script setup lang="ts">
useHead({
  title: "Chấm công",
  meta: [
    {
      name: "description",
      content: "timekeeping",
    },
  ],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  permission: [
    "SALE_OP",
    "SUPP_ADMIN",
    "ORG_ADMIN",
    "SALE_MANAGER",
    "SUPP_OP",
    "SALE",
    "SALES",
  ],
  name: "Chấm công",
});

const auth = useCookie("auth") as any;
const dataWorkEffort = ref<any>([]);
const selectedWorkEffortId = ref<any>();
const { getListWorkEfforts, createWorkEfforts } = useTimeKeeping();
const { fetchDataEmployees } = useOrder();
const { updateWorkEffortStatus } = useCrm();
const { storeId } = useTabContext();
const isLoading = ref(false);

// Reactive variables for date range and employee
const employeeId = ref<string>(auth.value?.user?.id || "");
const fromDate = ref<string>("");
const toDate = ref<string>("");
const handleGetWorkEffort = async (
  empId?: string,
  fromDateParam?: any,
  toDateParam?: any
) => {
  // Sử dụng reactive variables hoặc parameters
  const currentEmployeeId = empId || employeeId.value;
  const currentFromDate = fromDateParam || fromDate.value;
  const currentToDate = toDateParam || toDate.value;

  const attributes = {
    storeId: storeId.value,
    createdBy: currentEmployeeId,
  };

  try {
    const response = await getListWorkEfforts(
      currentEmployeeId,
      "",
      "TIMEKEEPING",
      0,
      10,
      attributes,
      currentFromDate,
      currentToDate
    );

    dataWorkEffort.value = response?.data;
    if (response?.data?.length > 0) {
      selectedWorkEffortId.value = response?.data[0];
    }
  } catch (error) {
    console.error("Error fetching work effort:", error);
  }
};
//

const handleCreateWorkEffort = async () => {
  isLoading.value = true;
  const attributes = {
    storeId: storeId.value,
  };
  try {
    const response = await createWorkEfforts(
      auth.value?.user?.id,
      dataWorkEffort.value[0]?.workflowId === "CHECK_IN"
        ? `${auth.value?.user?.name} ra ca  `
        : `${auth.value?.user?.name} vào ca`,
      "",
      dataWorkEffort.value[0]?.workflowId === "CHECK_IN"
        ? "CHECK_OUT"
        : "CHECK_IN",
      "TIMEKEEPING",
      attributes,
      ""
    );
    await handleGetWorkEffort();
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};

const handleSelectWorkEffort = (workEffort: string) => {
  selectedWorkEffortId.value = workEffort;
};
const dataEmployee = ref<any>([]);
const hanldeGetDataEmployee = async () => {
  try {
    const response = await fetchDataEmployees();
    dataEmployee.value = response;
  } catch (error) {
    throw error;
  }
};
const handleSelectedEmployee = async (employee: any) => {
  employeeId.value = employee?.id || auth.value?.user?.id;
  await handleGetWorkEffort();
};
const handleChangeDate = async (dateRange: any) => {
  fromDate.value = dateRange?.dateFrom || "";
  toDate.value = dateRange?.dateTo || "";
  await handleGetWorkEffort();
};
const completeWorkEffort = async (task: any) => {
  console.log("task", task?.id);
  console.log("data", dataWorkEffort.value);
  try {
    await updateWorkEffortStatus(
      auth.value?.user?.id,
      task.id,
      "TIMEKEEPING",
      "DONE"
    );
    await handleGetWorkEffort();
    useNuxtApp().$toast.success("Chúc mùng bạn đã hoàn thành công việc");
  } catch (error) {
    throw error;
  }
};
onMounted(async () => {
  // Khởi tạo reactive variables
  const today = new Date();
  const startOfDay = new Date(
    today.getFullYear(),
    today.getMonth(),
    today.getDate(),
    0,
    0,
    0,
    0
  );

  const endOfDay = new Date(
    today.getFullYear(),
    today.getMonth(),
    today.getDate(),
    23,
    59,
    59,
    999
  );

  // Set reactive variables
  employeeId.value = auth.value?.user?.id || "";
  fromDate.value = startOfDay.toISOString();
  toDate.value = endOfDay.toISOString();

  await Promise.allSettled([handleGetWorkEffort(), hanldeGetDataEmployee()]);
});
</script>
