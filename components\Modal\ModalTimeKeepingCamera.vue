<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center">
    <!-- Backdrop -->
    <div 
      class="absolute inset-0 bg-black bg-opacity-50"
      @click="handleClose"
    ></div>
    
    <!-- Modal Content -->
    <div class="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-hidden">
      <!-- Header -->
      <div class="flex items-center justify-between p-4 border-b">
        <h3 class="text-lg font-semibold text-gray-900">Chụp ảnh chấm công</h3>
        <button
          @click="handleClose"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Camera Content -->
      <div class="p-4">
        <!-- Preview Images -->
        <div v-if="previewImages.length" class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2">Ảnh đã chụp:</h4>
          <div class="grid grid-cols-3 gap-2">
            <div
              v-for="(image, index) in previewImages"
              :key="index"
              class="relative border border-primary rounded-md"
            >
              <img
                :src="image"
                alt="Preview"
                class="w-full h-20 object-cover rounded"
                loading="lazy"
              />
              <button
                @click="removeImage(index)"
                class="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-red-600"
              >
                ×
              </button>
            </div>
          </div>
        </div>

        <!-- Camera View -->
        <div v-if="isOpen" class="mb-4">
          <div class="relative bg-black rounded-lg overflow-hidden">
            <video 
              ref="videoElement" 
              autoplay 
              playsinline
              class="w-full h-64 object-cover"
            ></video>
            
            <!-- Camera Controls -->
            <div class="absolute bottom-4 left-0 right-0 flex justify-center gap-4">
              <button
                @click="handleTakePhoto"
                class="bg-white text-gray-800 rounded-full w-12 h-12 flex items-center justify-center shadow-lg hover:bg-gray-100 transition-colors"
              >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </button>
              
              <button
                @click="handleChangeCamera"
                class="bg-white text-gray-800 rounded-full w-12 h-12 flex items-center justify-center shadow-lg hover:bg-gray-100 transition-colors"
              >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Camera Toggle Button -->
        <div class="flex justify-center mb-4">
          <button
            @click="handleOpenCamera"
            class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-dark transition-colors flex items-center gap-2"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            {{ isOpen ? 'Đóng camera' : 'Mở camera' }}
          </button>
        </div>

        <!-- File Upload Alternative -->
        <div class="text-center mb-4">
          <span class="text-sm text-gray-500">hoặc</span>
        </div>
        
        <div class="flex justify-center">
          <label class="cursor-pointer">
            <input
              type="file"
              @change="handleImageUpload"
              accept="image/*"
              multiple
              class="hidden"
            />
            <span class="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center gap-2">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              Chọn ảnh từ thư viện
            </span>
          </label>
        </div>
      </div>

      <!-- Footer -->
      <div class="flex justify-end gap-3 p-4 border-t bg-gray-50">
        <button
          @click="handleClose"
          class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          Hủy
        </button>
        <button
          @click="handleConfirm"
          :disabled="!previewImages.length"
          class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Xác nhận
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";

const emit = defineEmits(["close", "capture"]);

// Reactive state
const previewImages = ref<string[]>([]);
const dataImage = ref<File[]>([]);
const isOpen = ref(false);
const videoElement = ref<HTMLVideoElement | null>(null);
const facingMode = ref<"user" | "environment">("environment");

// Methods
const handleClose = () => {
  closeCamera();
  emit("close");
};

const handleConfirm = () => {
  if (dataImage.value.length > 0) {
    emit("capture", {
      images: dataImage.value,
      previews: previewImages.value
    });
  }
};

const handleImageUpload = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = target.files;
  
  if (files) {
    Array.from(files).forEach((file) => {
      dataImage.value.push(file);
      
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          previewImages.value.push(e.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    });
  }
};

const removeImage = (index: number) => {
  previewImages.value.splice(index, 1);
  dataImage.value.splice(index, 1);
};

const handleOpenCamera = () => {
  isOpen.value = !isOpen.value;
  if (isOpen.value) {
    openCamera();
  } else {
    closeCamera();
  }
};

const openCamera = () => {
  navigator.mediaDevices
    .getUserMedia({
      video: {
        facingMode: facingMode.value,
      },
    })
    .then((stream) => {
      if (videoElement.value) {
        videoElement.value.srcObject = stream;
      }
    })
    .catch((err) => {
      console.error("Error accessing the camera:", err);
      useNuxtApp().$toast.error("Không thể truy cập camera");
    });
};

const closeCamera = () => {
  const stream = videoElement.value?.srcObject as MediaStream;
  if (stream) {
    const tracks = stream.getTracks();
    tracks.forEach((track) => track.stop());
  }
  isOpen.value = false;
};

const handleTakePhoto = () => {
  if (!videoElement.value) return;
  
  const canvas = document.createElement("canvas");
  canvas.width = videoElement.value.videoWidth;
  canvas.height = videoElement.value.videoHeight;

  const ctx = canvas.getContext("2d");
  if (ctx) {
    ctx.drawImage(videoElement.value, 0, 0, canvas.width, canvas.height);
    canvas.toBlob((blob) => {
      if (blob) {
        const file = new File([blob], `timekeeping-${Date.now()}.png`, { type: "image/png" });
        dataImage.value.push(file);
        const url = URL.createObjectURL(blob);
        previewImages.value.push(url);
      }
    }, "image/png");
  }
};

const handleChangeCamera = () => {
  facingMode.value = facingMode.value === "environment" ? "user" : "environment";
  closeCamera();
  openCamera();
};

// Cleanup on unmount
onUnmounted(() => {
  closeCamera();
});
</script>

<style scoped>
video {
  transform: scaleX(-1); /* Mirror effect for front camera */
}
</style>
