<template>
  <div class="min-h-screen bg-gray-100 p-4">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-2xl font-bold text-center mb-6">
        Test Task Camera Feature
      </h1>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- TaskList Component -->
        <div class="bg-white rounded-lg shadow p-4">
          <h2 class="text-lg font-semibold mb-4">Task List (Desktop View)</h2>
          <TaskList
            :dataWorkEffort="mockWorkEffort"
            :selectedWorkEffortId="selectedWorkEffortId"
            :isMobile="false"
            :dataEmployee="mockEmployees"
            @createWorkEffort="handleCreateWorkEffort"
            @selectWorkEffort="handleSelectWorkEffort"
            @completeWorkEffort="handleCompleteWorkEffort"
          />
        </div>

        <!-- Selected Task Info -->
        <div class="bg-white rounded-lg shadow p-4">
          <h2 class="text-lg font-semibold mb-4">Selected Task Info</h2>
          <div v-if="selectedWorkEffortId">
            <div class="space-y-2">
              <p><strong>ID:</strong> {{ selectedWorkEffortId.id }}</p>
              <p><strong>Name:</strong> {{ selectedWorkEffortId.name }}</p>
              <p>
                <strong>Status:</strong>
                {{ getStatusText(selectedWorkEffortId.status) }}
              </p>
              <p>
                <strong>Created:</strong>
                {{ formatDate(selectedWorkEffortId.createdStamp) }}
              </p>
            </div>

            <!-- TimeTrackingContent for viewing images -->
            <div class="mt-6">
              <h3 class="text-md font-medium mb-3">Time Tracking Images</h3>
              <div class="border rounded-lg h-64 overflow-hidden">
                <TimeTrackingContent
                  :selectedWorkEffortId="selectedWorkEffortId"
                />
              </div>
            </div>
          </div>
          <div v-else class="text-gray-500 text-center py-8">
            Chọn một task để xem thông tin chi tiết
          </div>
        </div>
      </div>

      <!-- Instructions -->
      <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold text-blue-800 mb-2">
          Hướng dẫn sử dụng:
        </h3>
        <ul class="text-blue-700 space-y-1">
          <li>
            • Trên desktop (!isMobile), mỗi task sẽ hiển thị nút "Chụp ảnh" nếu
            chưa có ảnh
          </li>
          <li>
            • Nếu task đã có ảnh, nút sẽ chuyển thành "Xem ảnh (số lượng)" màu
            xanh lá
          </li>
          <li>• Click "Chụp ảnh" để mở camera và chụp ảnh cho task</li>
          <li>• Click "Xem ảnh" để mở popup hiển thị tất cả ảnh của task đó</li>
          <li>• Trong popup có thể click vào ảnh để xem full size</li>
          <li>• Ảnh sẽ được lưu với parentType: "WORK_EFFORT_TIMEKEEPING"</li>
          <li>• Modal xem ảnh có grid layout responsive và navigation</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import TaskList from "~/components/TimeKeeping/TaskList.vue";
import TimeTrackingContent from "~/components/TimeKeeping/TimeTrackingContent.vue";

// Mock data
const mockWorkEffort = ref([
  {
    id: "task-1",
    name: "Công việc 1 - Kiểm tra hệ thống",
    workflowId: "CHECK_IN",
    status: 1,
    createdBy: "emp1",
    createdStamp: new Date().toISOString(),
  },
  {
    id: "task-2",
    name: "Công việc 2 - Báo cáo tiến độ",
    workflowId: "WORK",
    status: 2,
    createdBy: "emp2",
    createdStamp: new Date(Date.now() - 3600000).toISOString(),
  },
  {
    id: "task-3",
    name: "Công việc 3 - Hoàn thành task",
    workflowId: "CHECK_OUT",
    status: 4,
    createdBy: "emp1",
    createdStamp: new Date(Date.now() - 7200000).toISOString(),
  },
  {
    id: "task-4",
    name: "Công việc 4 - Review code",
    workflowId: "WORK",
    status: 1,
    createdBy: "emp2",
    createdStamp: new Date(Date.now() - 1800000).toISOString(),
  },
]);

const mockEmployees = ref([
  {
    id: "emp1",
    name: "Nguyễn Văn A",
  },
  {
    id: "emp2",
    name: "Trần Thị B",
  },
]);

const selectedWorkEffortId = ref(null);

// Event handlers
const handleCreateWorkEffort = () => {
  console.log("Create work effort clicked");
  useNuxtApp().$toast.success("Tạo công việc mới!");
};

const handleSelectWorkEffort = (workEffort: any) => {
  console.log("Selected work effort:", workEffort);
  selectedWorkEffortId.value = workEffort;
  useNuxtApp().$toast.info(`Đã chọn task: ${workEffort.name}`);
};

const handleCompleteWorkEffort = (task: any) => {
  console.log("Complete work effort:", task);
  // Update task status
  const taskIndex = mockWorkEffort.value.findIndex((t) => t.id === task.id);
  if (taskIndex !== -1) {
    mockWorkEffort.value[taskIndex].status = 4;
  }
  useNuxtApp().$toast.success("Hoàn thành công việc!");
};

// Helper functions
const getStatusText = (status: number) => {
  const statusMap = {
    1: "Đang thực hiện",
    2: "Tạm dừng",
    3: "Đang chờ",
    4: "Hoàn thành",
  };
  return statusMap[status] || "Không xác định";
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString("vi-VN");
};

// Page meta
definePageMeta({
  layout: false,
});
</script>

<style scoped>
/* Custom styles for test page */
</style>
